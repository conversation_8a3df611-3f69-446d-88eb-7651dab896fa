<?php

namespace App\Modules\Marketplaces\Http\Controllers\Wildberries;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Wildberries\Orders\OrderShowResource;
use App\Http\Resources\Marketplaces\Wildberries\Orders\UnmatchedItemsResource;
use App\Modules\Marketplaces\Http\Requests\LoadOrdersRequest;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesFBSPolicy;
use App\Modules\Marketplaces\Services\Wildberries\Services\OrdersService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

class OrdersController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WildberriesFBSPolicy $policy,
        private readonly OrdersService $ordersService
    ) {
    }

    /**
     * Получение детальной информации о заказе
     *
     * @response OrderShowResource
     */
    public function show(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $orderDetails = $this->ordersService->getOrderDetails($orderId);

            if (!$orderDetails) {
                return $this->errorResponse('Order not found', 404);
            }

            return $this->successResponse(OrderShowResource::make($orderDetails));
        });
    }

    /**
     * Получение списка несопоставленных товаров в заказе
     *
     * @response UnmatchedItemsResource
     */
    public function getUnmatchedItems(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $unmatchedItems = $this->ordersService->getUnmatchedItems($orderId);

            return $this->successResponse(UnmatchedItemsResource::make($unmatchedItems));
        });
    }

    /**
     * Загрузка всех типов заказов
     */
    public function loadAllOrders(LoadOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $dateFrom = $request->validated('date_from');
            $dateTo = $request->validated('date_to');

            $this->ordersService->loadAllOrders($integrationId, $dateFrom, $dateTo);

            return $this->noContentResponse();
        });
    }
}
