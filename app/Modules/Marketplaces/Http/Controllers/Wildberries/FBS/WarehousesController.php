<?php

namespace App\Modules\Marketplaces\Http\Controllers\Wildberries\FBS;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Wildberries\FBS\Warehouses\WarehouseIndexCollection;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Warehouses\GetFBSWarehousesRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Warehouses\LoadFBSWarehousesRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Warehouses\UpdateFBSWarehouseRequest;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesFBSPolicy;
use App\Modules\Marketplaces\Services\Wildberries\DTO\FBSUpdateDTO;
use App\Modules\Marketplaces\Services\Wildberries\Services\FBS\WarehousesService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS складами Wildberries
 */
class WarehousesController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WildberriesFBSPolicy $policy,
        private readonly WarehousesService $warehousesService
    ) {
    }

    /**
     * Получение списка FBS складов
     *
     * @response WarehouseIndexCollection<WarehouseIndexResource>
     */
    public function index(GetFBSWarehousesRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $warehouses = $this->warehousesService->getFbsWarehouses($integrationId);

            return $this->successResponse(new WarehouseIndexCollection($warehouses));
        });
    }

    /**
     * Загрузка FBS складов из API Wildberries
     */
    public function load(LoadFBSWarehousesRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $this->warehousesService->loadFbsWarehouses($integrationId);

            return $this->noContentResponse();
        });
    }

    /**
     * Обновление настроек FBS склада
     */
    public function update(
        UpdateFBSWarehouseRequest $request,
        string $integrationId,
        string $warehouseId
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $integrationId, $warehouseId) {
            $this->policy->checkPermissionsToIntegration($integrationId);
            $this->policy->checkPermissionsToWarehouse($warehouseId);

            $data = $request->validated();

            $this->warehousesService->updateFbsWarehouse(
                FBSUpdateDTO::fromArray(
                    array_merge($data, ['fbs_warehouse_id' => $warehouseId])
                )
            );

            return $this->noContentResponse();
        });
    }
}
