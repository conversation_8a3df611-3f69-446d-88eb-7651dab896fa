<?php

namespace App\Modules\Marketplaces\Http\Controllers\Ozon\FBO;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Ozon\FBO\Warehouses\ClusterCollection;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBO\GetFBOClustersListRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\UpdateFBOWarehouseRequest;
use App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBO\Warehouses\OzonFBOWarehouseIndexCollection;
use App\Modules\Marketplaces\Policies\Ozon\OzonPolicy;
use App\Modules\Marketplaces\Services\Ozon\DTO\UpdateFBOWarehouseDTO;
use App\Modules\Marketplaces\Services\Ozon\Services\FBO\FBOWarehousesService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS складами Wildberries
 */
class WarehousesController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly OzonPolicy $policy,
        private readonly FBOWarehousesService $warehousesService
    ) {
    }

    /**
     * Получение списка активных FBO складов
     *
     * Возможно нафиг не нужно
     * @response OzonFBOWarehouseIndexCollection<OzonFBOWarehouseIndexResource>
     */
    public function index(string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $warehouses = $this->warehousesService->get($integrationId);

            $collection = new OzonFBOWarehouseIndexCollection($warehouses);
            return $this->successResponse($collection);
        });
    }

    /**
     * Загрузка активных FBO складов из API Ozon
     *
     * Возможно нафиг не нужно
     */
    public function load(string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $this->warehousesService->load($integrationId);

            return $this->noContentResponse();
        });
    }

    /**
     * Обновление настроек FBO склада
     *
     * Возможно нафиг не нужно
     */
    public function update(
        UpdateFBOWarehouseRequest $request,
        string $integrationId,
        string $warehouseId
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $integrationId, $warehouseId) {
            $this->policy->checkPermissionsToIntegration($integrationId);
            $this->policy->checkPermissionsToWarehouse($warehouseId);

            $data = $request->validated();

            $this->warehousesService->update(
                UpdateFBOWarehouseDTO::fromArray(
                    array_merge($data, ['fbo_warehouse_id' => $warehouseId])
                )
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Получение списка кластеров Ozon
     *
     * @response ClusterCollection
     */
    public function getClusters(GetFBOClustersListRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId, $request) {
            $type = $request->validated('type');
            $this->policy->checkPermissionsToIntegration($integrationId);

            $clusters = $this->warehousesService->getClusters($integrationId, $type);

            return $this->successResponse(ClusterCollection::make($clusters));
        });
    }


}
