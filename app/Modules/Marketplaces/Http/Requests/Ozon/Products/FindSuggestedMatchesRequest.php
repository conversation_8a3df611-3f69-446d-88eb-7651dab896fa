<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\Products;

use App\Modules\Marketplaces\Enums\ProductMatchingTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FindSuggestedMatchesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'match_type' => [
                'required',
                'string',
                Rule::enum(ProductMatchingTypeEnum::class),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'match_type.required' => 'Тип сопоставления обязателен для заполнения.',
            'match_type.enum' => 'Недопустимый тип сопоставления.',
        ];
    }
}
