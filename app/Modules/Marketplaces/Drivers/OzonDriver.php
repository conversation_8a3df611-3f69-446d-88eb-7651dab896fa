<?php

namespace App\Modules\Marketplaces\Drivers;

use App\Modules\Marketplaces\DTO\CostAccounting\CostAccountingDto;
use App\Modules\Marketplaces\Services\Ozon\Services\OzonService;
use Exception;
use Illuminate\Support\Collection;
use Throwable;

class OzonDriver extends MarketplaceDriver
{
    protected readonly OzonService $service;

    public function __construct()
    {
        $this->service = new OzonService();
    }

    public function connect(string $id): string
    {
        return $this->service->connect($id);
    }

    /**
     * @throws Throwable
     */
    public function loadProducts(string $id): void
    {
        $this->service->loadProducts($id);
    }

    public function loadPrices(string $id): void
    {
        $this->service->loadPrices($id);
    }

    public function loadResidues(string $id): void
    {
        $this->service->loadResidues($id);
    }

    public function loadOrders(string $id, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        $this->service->loadOrders($id, $dateFrom, $dateTo);
    }

    public function loadReports(string $id, string $dateFrom, string $dateTo): void
    {
        $this->service->loadReports($id, $dateFrom, $dateTo);
    }

    public function disconnect(string $id): string
    {
        return $this->service->disconnect($id);
    }



    public function updateCostAccountingSettings(CostAccountingDto $dto, string $id): void
    {
        // TODO: Implement updateCostAccountingSettings() method.
    }
}
