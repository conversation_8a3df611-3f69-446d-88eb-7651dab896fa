<?php

namespace App\Modules\Marketplaces\Drivers;

use App\Modules\Marketplaces\DTO\CostAccounting\CostAccountingDto;
use Illuminate\Support\Collection;

abstract class MarketplaceDriver
{
    abstract public function connect(string $id): string;
    abstract public function disconnect(string $id): string;
    abstract public function loadProducts(string $id): void;
    abstract public function loadPrices(string $id): void;
    abstract public function loadResidues(string $id): void;
    abstract public function loadOrders(string $id, ?string $dateFrom = null, ?string $dateTo = null): void;
    abstract public function loadReports(string $id, string $dateFrom, string $dateTo): void;

    abstract public function updateCostAccountingSettings(CostAccountingDto $dto, string $id): void;
}
