<?php

namespace App\Modules\Marketplaces\Drivers;

use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\DTO\CostAccounting\CostAccountingDto;
use App\Modules\Marketplaces\Services\Wildberries\Services\WildberriesService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Throwable;

class WildberriesDriver extends MarketplaceDriver
{
    protected readonly WildberriesService $service;

    public function __construct()
    {
        $this->service = new WildberriesService();
    }

    public function connect(string $id): string
    {
        return $this->service->connect($id);
    }

    /**
     * @throws Throwable
     */
    public function loadProducts(string $id): void
    {
        $this->service->loadProducts($id);
    }

    /**
     * @throws WBSellerException
     * @throws BindingResolutionException
     */
    public function loadPrices(string $id): void
    {
        $this->service->loadPrices($id);
    }

    /**
     * @throws WBSellerException
     * @throws BindingResolutionException|Throwable
     */
    public function loadResidues(string $id): void
    {
        $this->service->loadResidues($id);
    }

    /**
     * @throws WBSellerException
     * @throws BindingResolutionException
     */
    public function loadOrders(string $id, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        $this->service->loadOrders($id, $dateFrom, $dateTo);
    }


    /**
     * Загрузка отчетов о продажах по реализации
     *
     * @param string $id ID интеграции
     * @param string $dateFrom Дата начала периода (RFC3339)
     * @param string $dateTo Дата окончания периода (RFC3339)
     * @throws Throwable
     */
    public function loadReports(string $id, string $dateFrom, string $dateTo): void
    {
        $this->service->loadReports($id, $dateFrom, $dateTo);
    }

    public function disconnect(string $id): string
    {
        return $this->service->disconnect($id);
    }





    public function updateCostAccountingSettings(CostAccountingDto $dto, string $id): void
    {
        $this->service->updateCostAccountingSettings($dto, $id);
    }
}
