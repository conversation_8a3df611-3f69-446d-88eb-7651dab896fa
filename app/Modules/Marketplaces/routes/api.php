<?php

use App\Http\Middleware\Api\OzoneWebhookMiddleware;
use App\Modules\Marketplaces\Http\Controllers\Ozon\FBO\FBOOrdersController;
use App\Modules\Marketplaces\Http\Controllers\Ozon\FBO\WarehousesController as FBOWarehousesController;
use App\Modules\Marketplaces\Http\Controllers\Ozon\FBS\CarriagesController;
use App\Modules\Marketplaces\Http\Controllers\Ozon\FBS\FBSOrdersController;
use App\Modules\Marketplaces\Http\Controllers\Ozon\FBS\StocksController;
use App\Modules\Marketplaces\Http\Controllers\Ozon\FBS\WarehousesController;
use App\Modules\Marketplaces\Http\Controllers\Ozon\FBS\WebhooksController;
use App\Modules\Marketplaces\Http\Controllers\Ozon\OzonController;
use App\Modules\Marketplaces\Http\Controllers\Ozon\ProductsController as OzonProductsController;
use App\Modules\Marketplaces\Http\Controllers\Wildberries\OrdersController;
use App\Modules\Marketplaces\Http\Controllers\Wildberries\ProductsController as WildberriesProductsController;
use App\Modules\Marketplaces\Http\Controllers\Wildberries\WildberriesController;
use Illuminate\Support\Facades\Route;

Route::pattern('type', 'ozon|wildberries');
Route::pattern('id', '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}');

Route::prefix('marketplaces')->group(function () {
    Route::get('/', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'index']);

    Route::post('/{type}/{id}/connect', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'connect']);
    Route::post('/{type}/{id}/disconnect', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'disconnect']);

    Route::post('/{type}/{id}/products/load', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'loadProducts']);
    Route::post('/{type}/{id}/orders/load', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'loadOrders']);

    Route::post('/{type}/{id}/reports/load', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'loadReports']);
    Route::put('/{type}/{id}/reports/cost-accounting/settings', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'updateCostAccountingSettings']);
    Route::post('/{type}/{id}/prices/load', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'loadPrices']);
    Route::post('/{type}/{id}/residues/load', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'loadResidues']);
    Route::post('/{type}/{id}/reports/load', [App\Modules\Marketplaces\Http\Controllers\MarketplaceController::class, 'loadReports']);

    // Маршруты для работы с товарами Wildberries
    Route::prefix('wildberries/{integrationId}/products')->group(function () {
        Route::get('/matched', [WildberriesProductsController::class, 'getMatchedProducts']);
        Route::get('/to-match', [WildberriesProductsController::class, 'getProductsToMatch']);
        Route::post('/find-matches', [WildberriesProductsController::class, 'findSuggestedMatches']);
        Route::post('/manual-match', [WildberriesProductsController::class, 'manualMatch']);
    });

    // Маршруты для работы с товарами Ozon
    Route::prefix('ozon/{integrationId}/products')->group(function () {
        Route::get('/matched', [OzonProductsController::class, 'getMatchedProducts']);
        Route::get('/to-match', [OzonProductsController::class, 'getProductsToMatch']);
        Route::post('/find-matches', [OzonProductsController::class, 'findSuggestedMatches']);
        Route::post('/manual-match', [OzonProductsController::class, 'manualMatch']);
    });

    Route::post('/wildberries/', [WildberriesController::class, 'store']);
    Route::get('/wildberries/{id}', [WildberriesController::class, 'show']);
    Route::put('/wildberries/{id}', [WildberriesController::class, 'update']);
    Route::delete('/wildberries/{id}', [WildberriesController::class, 'destroy']);

    Route::get('wildberries/orders/load-all', [OrdersController::class, 'loadAllOrders']);
    Route::get('wildberries/orders/{order_id}', [OrdersController::class, 'show']);
    Route::get('wildberries/orders/{order_id}/unmatched-items', [OrdersController::class, 'getUnmatchedItems']);

    require base_path('/app/Modules/Marketplaces/routes/wildberries/fbs.php');

    require base_path('/app/Modules/Marketplaces/routes/wildberries/dbs.php');

    require base_path('/app/Modules/Marketplaces/routes/wildberries/self-delivery.php');

    Route::prefix('ozon')->group(function () {
        Route::post('/', [OzonController::class, 'store']);
        Route::get('/{id}', [OzonController::class, 'show']);
        Route::put('/{id}', [OzonController::class, 'update']);
        Route::delete('/{id}', [OzonController::class, 'destroy']);

        Route::post('/webhooks', [WebhooksController::class])->middleware(OzoneWebhookMiddleware::class);

        Route::prefix('fbs')->group(function () {
            Route::get('/{integration_id}/warehouses', [WarehousesController::class, 'index']);
            Route::post('/{integration_id}/warehouses/load', [WarehousesController::class, 'load']);
            Route::put('/{integration_id}/warehouses/{warehouse_id}', [WarehousesController::class, 'update']);
            Route::get('/{integration_id}/warehouses/{warehouse_id}/delivery-methods', [WarehousesController::class, 'getDeliveryMethods']);

            Route::post('/{integration_id}/orders/load', [FBSOrdersController::class, 'loadOrders']);
            Route::get('/{integration_id}/orders', [FBSOrdersController::class, 'indexFbs']);
            Route::post('/{integration_id}/orders/delivering/set', [FBSOrdersController::class, 'setDeliveringStatus']);
            Route::post('/{integration_id}/orders/last-mile/set', [FBSOrdersController::class, 'setLastMileStatus']);
            Route::post('/{integration_id}/orders/delivered/set', [FBSOrdersController::class, 'setDeliveredStatus']);

            Route::post('/{integration_id}/stocks/sync', [StocksController::class, 'syncStocks']);

            Route::post('/orders/{order_id}/confirm', [FBSOrdersController::class, 'confirmOrder']);
            Route::post('/orders/{order_id}/cutoff/set', [FBSOrdersController::class, 'setCutoffDate']);
            Route::post('/orders/{order_id}/timeslot/set', [FBSOrdersController::class, 'setTimeslot']);
            Route::get('/orders/{order_id}/timeslot/change-restrictions', [FBSOrdersController::class, 'getTimeslotChangeRestrictions']);

            Route::post('/orders/{order_id}/cancel/items', [FBSOrdersController::class, 'cancelOrderItems']);
            Route::post('/orders/{order_id}/cancel', [FBSOrdersController::class, 'cancelOrder']);
            Route::post('/orders/{order_id}/product/country', [FBSOrdersController::class, 'setProductCountry']);
            Route::get('/orders/cancel-reason', [FBSOrdersController::class, 'getCancelReason']);
            Route::post('/orders/package-labels', [FBSOrdersController::class, 'getPackageLabels']);

            Route::post('/orders/{order_id}/collect', [FBSOrdersController::class, 'collectOrder']);

            Route::get('/carriages', [CarriagesController::class, 'index']);
            Route::post('/carriages', [CarriagesController::class, 'create']);
            Route::get('/carriages/{carriage_id}', [CarriagesController::class, 'show']);
            Route::post('/carriages/{carriage_id}/approve', [CarriagesController::class, 'approve']);
            Route::post('/carriages/{carriage_id}/set-postings', [CarriagesController::class, 'setPostings']);
            Route::delete('/carriages/{carriage_id}', [CarriagesController::class, 'cancel']);
            Route::post('/carriages/check-documents', [CarriagesController::class, 'checkDocuments']);
            Route::post('/carriages/check-waybill', [CarriagesController::class, 'checkWaybill']);
            Route::get('/carriages/{carriage_id}/barcode', [CarriagesController::class, 'getBarcode']);
            Route::get('/carriages/{carriage_id}/barcode/download', [CarriagesController::class, 'downloadBarcode']);
            Route::get('/carriages/{carriage_id}/documents', [CarriagesController::class, 'getDocuments']);
            Route::get('/carriages/{carriage_id}/documents/download', [CarriagesController::class, 'downloadDocuments']);
            Route::get('/carriages/{carriage_id}/digital-document', [CarriagesController::class, 'getDigitalDocument']);
            Route::get('/carriages/{carriage_id}/digital-document/download', [CarriagesController::class, 'downloadDigitalDocument']);

            // Пропуски
            Route::post('/carriages/{carriage_id}/arrival-pass', [CarriagesController::class, 'createArrivalPass']);
            Route::put('/carriages/{carriage_id}/arrival-pass', [CarriagesController::class, 'updateArrivalPass']);
            Route::delete('/carriages/{carriage_id}/arrival-pass', [CarriagesController::class, 'deleteArrivalPass']);
            Route::get('/carriages/{carriage_id}/arrival-passes', [CarriagesController::class, 'getArrivalPasses']);
            Route::get('/arrival-passes/{pass_id}', [CarriagesController::class, 'showArrivalPass']);
        });

        Route::prefix('fbo')->group(function () {
            Route::get('/{integration_id}/warehouses', [FBOWarehousesController::class, 'index']);
            Route::get('/{integration_id}/clusters', [FBOWarehousesController::class, 'getClusters']);
            Route::post('/{integration_id}/warehouses/load', [FBOWarehousesController::class, 'load']);
            Route::put('/{integration_id}/warehouses/{id}', [FBOWarehousesController::class, 'update']);

            Route::get('/{integration_id}/orders', [FBOOrdersController::class, 'index']);
            Route::post('/{integration_id}/orders/load', [FBOOrdersController::class, 'loadOrders']);
        });
    });
});
