<?php

namespace App\Http\Controllers\Api\Internal\Money\OutgoingPayments;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Finances\OutgoingPaymentPolicyContract;
use App\Contracts\Services\Internal\Finances\OutgoingPaymentsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\OutgoingPayments\OutgoingPaymentIndexRequest;
use App\Http\Requests\Api\Internal\OutgoingPayments\OutgoingPaymentStoreRequest;
use App\Http\Requests\Api\Internal\OutgoingPayments\OutgoingPaymentUpdateRequest;
use App\Http\Resources\Money\OutgoingPayments\OutgoingPaymentIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OutgoingPaymentController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly OutgoingPaymentsServiceContract $service,
        private readonly OutgoingPaymentPolicyContract $policy
    ) {
    }

    /**
     * @response OutgoingPaymentIndexCollection<OutgoingPaymentIndexResource>
     */
    public function index(OutgoingPaymentIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->validated());
            $collection = new OutgoingPaymentIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(OutgoingPaymentStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response OutgoingPaymentShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(OutgoingPaymentShowResource::make($data));
        });
    }

    public function update(OutgoingPaymentUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
