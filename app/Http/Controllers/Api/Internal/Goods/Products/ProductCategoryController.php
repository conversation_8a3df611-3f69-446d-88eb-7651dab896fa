<?php

namespace App\Http\Controllers\Api\Internal\Goods\Products;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Products\ProductCategoryPolicyContract;
use App\Contracts\Services\Internal\Products\ProductCategoriesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ProductCategories\ProductCategoryStoreRequest;
use App\Http\Requests\Api\Internal\ProductCategories\ProductCategoryUpdateRequest;
use App\Http\Resources\Goods\Categories\ProductCategoryIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductCategoryController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ProductCategoriesServiceContract $service,
        private readonly ProductCategoryPolicyContract $policy
    ) {
    }

    /**
     * @response ProductCategoryIndexCollection<ProductCategoryIndexResource>
     */
    public function index(string $cabinet_id): JsonResponse
    {
        return $this->executeAction(function () use ($cabinet_id) {
            $this->policy->index($cabinet_id);

            $data = $this->service->index(['cabinet_id' => $cabinet_id]);
            $collection = new ProductCategoryIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(ProductCategoryStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response ProductCategoryShowResource
     */
    public function show(Request $request, string $cabinetId, string $category_id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $category_id) {
            $this->authorizeView($request, $category_id);

            $data = $this->service->show($category_id);
            return $this->successResponse(ProductCategoryShowResource::make($data));
        });
    }

    public function update(ProductCategoryUpdateRequest $request, string $cabinetId, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $cabinetId, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
