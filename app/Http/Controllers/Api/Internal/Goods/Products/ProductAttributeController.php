<?php

namespace App\Http\Controllers\Api\Internal\Goods\Products;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Products\ProductAttributePolicyContract;
use App\Contracts\Services\Internal\Products\ProductAttributesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ProductAttributes\ProductAttributesIndexRequest;
use App\Http\Requests\Api\Internal\ProductAttributes\ProductAttributeStoreRequest;
use App\Http\Requests\Api\Internal\ProductAttributes\ProductAttributesUpdateRequest;
use App\Http\Resources\Goods\Products\ProductAttributeIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductAttributeController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly ProductAttributesServiceContract $service,
        private readonly ProductAttributePolicyContract $policy
    ) {
    }

    /**
     * @response ProductAttributeIndexCollection<ProductAttributeIndexResource>
     */
    public function index(ProductAttributesIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $this->policy->index($request->validated('product_id'));

            $data = $this->service->index($request->validated());
            $collection = new ProductAttributeIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function getByProductId(ProductAttributesIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $this->policy->getByProductId($request->validated('product_id'));

            $data = $this->service->getByProductId($request->validated());
            return $this->successResponse($data);
        });
    }

    public function store(ProductAttributeStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response ProductAttributeShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(ProductAttributeShowResource::make($data));
        });
    }

    public function update(ProductAttributesUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
