<?php

namespace App\Http\Controllers\Api\Internal\Goods\Products;

use App\Http\Controllers\Controller;
use App\Http\Resources\Goods\Products\ProductSizIndexResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class ProductSizController extends Controller
{
    /**
     * @response ProductSizIndexResource
     */
    public function index(): JsonResponse
    {

        $siz['sizNames'] = DB::table('product_siz_names')->get();
        $siz['sizTypes'] = DB::table('product_siz_types')->get();

        return response()->json(ProductSizIndexResource::make($siz), 201);

    }

}
