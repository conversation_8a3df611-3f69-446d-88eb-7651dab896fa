<?php

namespace App\Http\Controllers\Api\Internal\Goods\Transfers;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Products\GoodsTransferPolicyContract;
use App\Contracts\Services\Internal\Products\GoodsTransferServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\GoodsTransfer\GoodsTransferIndexRequest;
use App\Http\Requests\Api\Internal\GoodsTransfer\GoodsTransferStoreRequest;
use App\Http\Requests\Api\Internal\GoodsTransfer\GoodsTransferUpdateRequest;
use App\Http\Resources\Goods\Transfers\GoodsTransferIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GoodsTransferController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly GoodsTransferServiceContract $service,
        private readonly GoodsTransferPolicyContract $policy
    ) {
    }

    /**
     * @response GoodsTransferIndexCollection<GoodsTransferIndexResource>
     */
    public function index(GoodsTransferIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $data = $this->service->index($data);
            $collection = new GoodsTransferIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(GoodsTransferStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response GoodsTransferShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(GoodsTransferShowResource::make($data));
        });
    }

    public function update(GoodsTransferUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
