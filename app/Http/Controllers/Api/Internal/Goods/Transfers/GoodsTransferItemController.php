<?php

namespace App\Http\Controllers\Api\Internal\Goods\Transfers;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Products\GoodsTransferItemPolicyContract;
use App\Contracts\Services\Internal\Products\GoodsTransferItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\GoodsTransferItems\GoodsTransferItemIndexRequest;
use App\Http\Requests\Api\Internal\GoodsTransferItems\GoodsTransferItemStoreRequest;
use App\Http\Requests\Api\Internal\GoodsTransferItems\GoodsTransferItemUpdateRequest;
use App\Http\Resources\Goods\Transfers\GoodsTransferItemIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GoodsTransferItemController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly GoodsTransferItemsServiceContract $service,
        private readonly GoodsTransferItemPolicyContract $policy
    ) {
    }

    /**
     * @response GoodsTransferItemIndexCollection<GoodsTransferItemIndexResource>
     */
    public function index(GoodsTransferItemIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);
            $data = $this->service->index($data);
            $collection = new GoodsTransferItemIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(GoodsTransferItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response GoodsTransferItemShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(GoodsTransferItemShowResource::make($data));
        });
    }

    public function update(GoodsTransferItemUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
