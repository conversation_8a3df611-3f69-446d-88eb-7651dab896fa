<?php

namespace App\Http\Controllers\Api\Internal\Goods\Attributes;

use App\Contracts\Policies\AttributeGroupPolicyContract;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Services\Internal\AttributeGroupsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\AttributeGroups\AttributeGroupIndexRequest;
use App\Http\Requests\Api\Internal\AttributeGroups\AttributesGroupsStoreRequest;
use App\Http\Requests\Api\Internal\AttributeGroups\AttributesGroupsUpdateRequest;
use App\Http\Resources\Goods\Attributes\Groups\AttributeGroupIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AttributeGroupsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly AttributeGroupsServiceContract $service,
        private readonly AttributeGroupPolicyContract $policy
    ) {
    }

    /**
     * @response AttributeGroupIndexCollection<AttributeGroupIndexResource>
     */
    public function index(AttributeGroupIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $result = $this->service->index($data);
            $collection = new AttributeGroupIndexCollection($result);
            return $this->successResponse($collection);
        });
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AttributesGroupsStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * Display the specified resource.
     */
    /**
     * @response AttributeGroupShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(AttributeGroupShowResource::make($data));
        });
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AttributesGroupsUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
