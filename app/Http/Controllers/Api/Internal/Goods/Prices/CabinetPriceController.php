<?php

namespace App\Http\Controllers\Api\Internal\Goods\Prices;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Cabinet\CabinetPricePolicyContract;
use App\Contracts\Services\Internal\Finances\CabinetPricesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\CabinetPrices\CabinetPriceCreateRequest;
use App\Http\Requests\Api\Internal\CabinetPrices\CabinetPriceIndexRequest;
use App\Http\Requests\Api\Internal\CabinetPrices\CabinetPriceUpdateRequest;
use App\Http\Resources\Goods\Prices\CabinetPriceIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CabinetPriceController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly CabinetPricesServiceContract $service,
        private readonly CabinetPricePolicyContract $policy
    ) {
    }

    /**
     * @response CabinetPriceIndexCollection<CabinetPriceIndexResource>
     */
    public function index(CabinetPriceIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new CabinetPriceIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }
    /**
     * @response CabinetPriceShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(CabinetPriceShowResource::make($data));
        });
    }

    public function update(CabinetPriceUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function store(CabinetPriceCreateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
