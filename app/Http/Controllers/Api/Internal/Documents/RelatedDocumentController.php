<?php

namespace App\Http\Controllers\Api\Internal\Documents;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\RelatedDocumentPolicyContract;
use App\Contracts\Services\Internal\RelatedDocumentsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\RelatedDocuments\RelatedDocumentIndexRequest;
use App\Http\Requests\Api\Internal\RelatedDocuments\RelatedDocumentStoreRequest;
use App\Http\Resources\Documents\RelatedDocumentIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RelatedDocumentController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly RelatedDocumentsServiceContract $service,
        private readonly RelatedDocumentPolicyContract $policy
    ) {
    }

    /**
     * @response RelatedDocumentIndexCollection<RelatedDocumentIndexResource>
     */
    public function index(RelatedDocumentIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $documentId = $request->validated('document_id');
            $this->policy->index($documentId);

            $data = $this->service->index(['document_id' => $documentId]);
            $collection = new RelatedDocumentIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(RelatedDocumentStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }
    /**
     * @response RelatedDocumentShowResource
     */
    public function show(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(RelatedDocumentShowResource::make($data));
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
