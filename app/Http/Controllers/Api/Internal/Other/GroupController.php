<?php

namespace App\Http\Controllers\Api\Internal\Other;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Groups\GroupStoreRequest;
use App\Http\Resources\Other\Groups\GroupShowCollection;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Throwable;

class GroupController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(GroupStoreRequest $request): JsonResponse
    {

        /**
         *
         * создать группу, для компании с названием и привязать к контрагенту!
         *
         * Если группа нигде не используется, она удаляется. Сделать проверку по id!
         * Если группа существует, не создавать новую, проверка по названию.
         *
         */

        try {

            // Если нет названия групп, то пропускаем создание и удаление
            if ($request['title'] != null) {

                // Если нет такой группы, то дбавляем новую группу
                foreach ($request['title'] as $title) {

                    $group = DB::table('groups')
                        ->where('cabinet_id', $request['cabinet_id'])
                        ->where('title', $title)
                        ->first();

                    if (!$group) {

                        $id = Str::orderedUuid()->toString();

                        $employee_id = DB::table('employees')->where('user_id', auth()->user()->id)->first();

                        DB::table('groups')
                            ->insertGetId([
                                'id' => $id,
                                'cabinet_id' => $request['cabinet_id'],
                                'title' => $title,
                                'employee_id' => $employee_id->id,
                                'department_id' => $request['department_id'],
                            ]);

                        // Добавляем связь между группой и контрагентами, и удаляем несуществующие связи!

                        DB::table('contractor_group')
                            ->insert([
                                'contractor_id' => $request['contractor_id'],
                                'group_id' => $id
                            ]);
                    }
                }

            } else {

                //TODO лучше все же перепроверить, должен ли он удалять все группы или только неиспользованные
                // ContractorEntity::find($request['contractor_id'])->groups()->sync([]);
                DB::table('contractor_group')
                    ->where([
                        'contractor_id' => $request['contractor_id'],
                    ])
                    ->delete();

            }

            // Достаём всех контрагентов данного пользователя

            $contractorsUser = DB::table('contractors')
                ->where('cabinet_id', $request['cabinet_id'])
                ->pluck('id');

            if ($contractorsUser->isNotEmpty()) {

                $GroupIds = DB::table('contractor_group')
                    ->whereIn('contractor_id', $contractorsUser)
                    ->pluck('contractor_id');

                if ($GroupIds->isNotEmpty()) {

                    $group_id_is = DB::table('contractor_group')
                        ->whereIn('contractor_id', $GroupIds)
                        ->pluck('group_id')
                        ->unique();

                    if ($group_id_is !== null) {
                        // Удаляем лишние группы
                        DB::table('groups')
                            ->where('cabinet_id', $request['cabinet_id'])
                            ->whereNotIn('id', $group_id_is)
                            ->delete();

                    }

                } else {
                    // Удаляем лишние группы
                    DB::table('groups')
                        ->where('cabinet_id', $request['cabinet_id'])
                        ->delete();

                }
            }

            $groups = DB::table('groups')
                ->where('cabinet_id', $request['cabinet_id'])
                ->first(); // Возвращаем для постмана id

            return response()->json($groups, 201);


        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
            ], 500);
        }
    }

    /**
     * @response GroupShowCollection<GroupShowResource>
     */
    public function show(string $id): JsonResponse
    {
        $group = DB::table('groups')
            ->where('cabinet_id', $id)
            ->get();

        $collection = new GroupShowCollection($group);
        return response()->json($collection, 200);

    }

}
