<?php

namespace App\Http\Controllers\Api\Internal\Other\Status;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\StatusPolicyContract;
use App\Contracts\Services\Internal\StatusesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Statuses\StatusIndexRequest;
use App\Http\Requests\Api\Internal\Statuses\StatusStoreRequest;
use App\Http\Requests\Api\Internal\Statuses\StatusUpdateRequest;
use App\Http\Resources\Other\Status\StatusIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class StatusController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly StatusesServiceContract $service,
        private readonly StatusPolicyContract $policy
    ) {
    }

    /**
     * @response StatusIndexCollection<StatusIndexResource>
     */
    public function index(StatusIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new StatusIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(StatusStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response StatusShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(StatusShowResource::make($data));
        });
    }

    public function update(StatusUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
