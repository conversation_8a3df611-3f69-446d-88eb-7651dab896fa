<?php

namespace App\Http\Controllers\Api\Internal\Other;

use App\Contracts\Services\Internal\SuggestsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Dadata\AddressRequest;
use App\Http\Requests\Api\Internal\Dadata\BankRequest;
use App\Http\Requests\Api\Internal\Dadata\FindPartyRequest;
use App\Http\Resources\Other\Suggests\SuggestPartyResource;
use Illuminate\Http\JsonResponse;

class SuggestController extends Controller
{
    public function __construct(
        private readonly SuggestsServiceContract $service
    ) {
    }

    /**
     * @response SuggestPartyResource[]
     */
    public function findParty(FindPartyRequest $request): JsonResponse
    {
        $result = $this->service->party($request->toDTO());

        return response()->json(SuggestPartyResource::collection($result), 200);
    }

    /**
     * @response SuggestBankResource[]
     */
    public function bank(BankRequest $request): JsonResponse
    {
        $result = $this->service->bank($request->toDTO());

        return response()->json(SuggestBankResource::collection($result), 200);
    }

    /**
     * @response SuggestAddressResource[]
     */
    public function address(AddressRequest $request): JsonResponse
    {
        $result = $this->service->address($request->toDTO());

        return response()->json(SuggestAddressResource::collection($result), 200);
    }
}
