<?php

namespace App\Http\Controllers\Api\Internal\Workspace\Cabinet;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Cabinet\CabinetInvitePolicyContract;
use App\Contracts\Services\Internal\Cabinet\CabinetInvitesServiceContract;
use App\DTO\UserDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\CabinetInvites\CabinetInviteAcceptRequest;
use App\Http\Requests\Api\Internal\CabinetInvites\CabinetInviteIndexRequest;
use App\Http\Requests\Api\Internal\CabinetInvites\CabinetInviteStoreRequest;
use App\Http\Resources\Workspace\Cabinet\CabinetInviteIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CabinetInviteController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly CabinetInvitesServiceContract $service,
        private readonly CabinetInvitePolicyContract $policy
    ) {
    }

    /**
     * @response CabinetInviteIndexCollection<CabinetInviteIndexResource>
     */
    public function index(CabinetInviteIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new CabinetInviteIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function received(): ?JsonResponse
    {
        return $this->executeAction(function () {
            $data = $this->service->getReceivedInvites();
            return $this->successResponse($data);
        });
    }

    public function store(CabinetInviteStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response CabinetInviteShowResource
     */
    public function show(Request $request, string $token): JsonResponse
    {
        return $this->executeAction(function () use ($request, $token) {
            $this->authorizeView($request, $token);

            $userDTO = new UserDTO($request->user()->id, $request->user()->email);
            $data = $this->service->show($token, $userDTO);
            return $this->successResponse(CabinetInviteShowResource::make($data));
        });
    }

    public function accept(CabinetInviteAcceptRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {

            $requestUser = $request->user();

            $userDTO = new UserDTO(
                $requestUser->id,
                $requestUser->email,
                $requestUser->lastname,
                $requestUser->firstname,
                $requestUser->patronymic
            );

            $this->policy->acceptOrDecline($requestUser, $id);

            $this->service->accept($id, $userDTO);
            return $this->noContentResponse();
        });
    }

    public function decline(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $requestUser = $request->user();

            $this->policy->acceptOrDecline($requestUser, $id);

            $this->service->decline($id);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
