<?php

namespace App\Http\Controllers\Api\Internal\Procurement;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\PackingPolicyContract;
use App\Contracts\Services\Internal\PackingsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Packings\PackingCreateRequest;
use App\Http\Requests\Api\Internal\Packings\PackingIndexRequest;
use App\Http\Requests\Api\Internal\Packings\PackingUpdateRequest;
use App\Http\Resources\References\Packings\PackingIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PackingController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly PackingsServiceContract $service,
        private readonly PackingPolicyContract $policy
    ) {
    }

    /**
     * @response PackingIndexCollection<PackingIndexResource>
     */
    public function index(PackingIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new PackingIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    /**
     * Store a newly created resource in storage.
     * PackingCreateRequest
     */
    public function store(PackingCreateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }


    /**
     * @response PackingShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(PackingShowResource::make($data));
        });
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PackingUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
