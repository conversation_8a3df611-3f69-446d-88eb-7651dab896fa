<?php

namespace App\Http\Controllers\Api\Internal\User;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\UserViewSettingsPolicyContract;
use App\Contracts\Services\Internal\UserViewSettingsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\UserViewSettings\UserViewSettingsIndexRequest;
use App\Http\Requests\Api\Internal\UserViewSettings\UserViewSettingsUpdateRequest;
use App\Http\Resources\User\UserViewSettings\UserViewSettingsIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UserViewSettingsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly UserViewSettingsServiceContract $service,
        private readonly UserViewSettingsPolicyContract $policy
    ) {
    }

    /**
     * @response UserViewSettingsIndexCollection<UserViewSettingsIndexResource>
     */
    public function index(UserViewSettingsIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index(
                [
                    'cabinet_id' => $request->validated('cabinet_id'),
                    'name' => $request->validated('name'),
                    'user_id' => $request->user()->id
                ]
            );
            $collection = new UserViewSettingsIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function update(UserViewSettingsUpdateRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
