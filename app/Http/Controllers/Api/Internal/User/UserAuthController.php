<?php

namespace App\Http\Controllers\Api\Internal\User;

use App\Enums\Api\Internal\CabinetInviteStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\UserAuthRequest;
use App\Http\Resources\User\UserProfileResource;
use App\Models\User;
use App\Services\UserSettingsService;
use App\Traits\HasOrderedUuid;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Throwable;

class UserAuthController extends Controller
{
    use HasOrderedUuid;
    use RegistersUsers;

    public function register(UserAuthRequest $request): JsonResponse
    {
        try {

            $validator = $request->validated();

            $user = User::create([
                'lastname' => $validator['lastname'] ?? null,
                'firstname' => $validator['firstname'],
                'patronymic' => $validator['patronymic'] ?? null,
                'tel' => $validator['tel'],
                'email' => $validator['email'],
                'password' => $validator['password'],
            ]);

            if (isset($validator['invite_token'])) {
                $this->acceptInviteHandle($validator, $user);
            }

            UserSettingsService::storeSettings($user->id);

            //event(new Registered($user));

            return response()->json([
                'token' => $user->createToken('Api-token')->plainTextToken
            ], 201);

        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
            ], 500);
        }
    }

    public function login(Request $request): JsonResponse
    {
        try {

            $validator = Validator::make($request->all(), [
                'email' => 'required|string|email',
                'password' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation error',
                    'data' => $validator->errors()
                ], 401);
            }

            $email = strtolower($request->email);
            if (!Auth::attempt(
                [
                    'email' => $email,
                    'password' => $request->password
                ]
            )) {
                return response()->json([
                    'message' => 'Email or password error',
                ], 401);
            }

            $user = User::where('email', $email)->first();

            if (!$user) {
                return response()->json([
                    'message' => 'User error',
                ], 401);
            }

            return response()->json([
                'token' => $user->createToken('Api-token')->plainTextToken,
                'data' => $user,
            ], 200);


        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
            ], 500);
        }
    }

    /**
     * @response UserProfileResource
     */
    public function profile(): JsonResponse
    {
        $userData = Auth::user()->load('userSettings');

        return response()->json(UserProfileResource::make($userData), 200);
    }

    public function logout(): JsonResponse
    {
        auth()->user()->tokens()->delete();
        return response()->json([], 204);
    }

    public function destroy(): JsonResponse
    {
        DB::table('users')
            ->where('id', Auth::user()->id)
            ->delete();

        return response()->json([], 204);
    }

    public function acceptInviteHandle(array $validator, User $user): void
    {
        $invite = DB::table('cabinet_invites')
            ->where('token', $validator['invite_token'])
            ->where('email', $validator['email'])
            ->first();

        if ($invite) {
            $employeeId = $this->generateUuid();
            DB::table('employees')
                ->insert([
                    'id' => $employeeId,
                    'user_id' => $user->id,
                    'lastname' => $validator['lastname'] ?? null,
                    'firstname' => $validator['firstname'],
                    'patronymic' => $validator['patronymic'] ?? null,
                    'email' => $validator['email'],
                    'status' => 'active',
                    'created_at' => Carbon::now(),
                    'role_id' => $invite->role_id,
                    'department_id' => $invite->department_id
                ]);
            DB::table('cabinet_employee')
                ->insert([
                    'created_at' => Carbon::now(),
                    'cabinet_id' => $invite->cabinet_id,
                    'employee_id' => $employeeId
                ]);

            DB::table('cabinet_invites')
                ->where('token', $validator['invite_token'])
                ->update(['status' => CabinetInviteStatusEnum::ACCEPTED]);

            DB::table('users')
                ->where('id', $user->id)
                ->update(['current_cabinet_id' => $invite->cabinet_id]);
        }
    }

}
