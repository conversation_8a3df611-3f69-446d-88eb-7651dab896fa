<?php

namespace App\Http\Controllers\Api\Internal\References;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\ProfitTaxRatePolicyContract;
use App\Contracts\Services\Internal\Directories\ProfitTaxRatesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ProfitTaxRates\ProfitTaxRateIndexRequest;
use App\Http\Requests\Api\Internal\ProfitTaxRates\ProfitTaxRateStoreRequest;
use App\Http\Requests\Api\Internal\ProfitTaxRates\ProfitTaxRateUpdateRequest;
use App\Http\Resources\References\ProfitTaxRates\ProfitTaxRateIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProfitTaxRateController extends Controller
{
    use ApiPolicy;

    public function __construct(
        public readonly ProfitTaxRatesServiceContract $service,
        private readonly ProfitTaxRatePolicyContract $policy
    ) {
    }

    /**
     * @response ProfitTaxRateIndexCollection<ProfitTaxRateIndexResource>
     */
    public function index(ProfitTaxRateIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $result = $this->service->index($data);
            $collection = new ProfitTaxRateIndexCollection($result);
            return $this->successResponse($collection);
        });
    }

    public function store(ProfitTaxRateStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response ProfitTaxRateShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(ProfitTaxRateShowResource::make($data));
        });
    }

    public function update(ProfitTaxRateUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
