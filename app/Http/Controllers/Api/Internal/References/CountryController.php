<?php

namespace App\Http\Controllers\Api\Internal\References;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\CountryPolicyContract;
use App\Contracts\Services\Internal\Directories\CountriesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Countries\CountryIndexRequest;
use App\Http\Requests\Api\Internal\Countries\CountryStoreRequest;
use App\Http\Requests\Api\Internal\Countries\CountryUpdateRequest;
use App\Http\Resources\References\Countries\CountryIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CountryController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly CountriesServiceContract $service,
        private readonly CountryPolicyContract $policy
    ) {
    }

    /**
     * @response CountryIndexCollection<CountryIndexResource>
     */
    public function index(CountryIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            $collection = new CountryIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CountryStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response CountryShowResource
     */
    public function show(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(CountryShowResource::make($data));
        });
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CountryUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
