<?php

namespace App\Http\Controllers\Api\Internal\References\MeasurementUnits;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\MeasurementUnitGroupPolicyContract;
use App\Contracts\Services\Internal\Directories\MeasurementUnitGroupServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\MeasurementUnitGroups\MeasurementUnitGroupIndexRequest;
use App\Http\Requests\Api\Internal\MeasurementUnitGroups\MeasurementUnitGroupStoreRequest;
use App\Http\Requests\Api\Internal\MeasurementUnitGroups\MeasurementUnitGroupUpdateRequest;
use App\Http\Resources\References\MeasurementUnits\MeasurementUnitGroupIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MeasurementUnitGroupController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly MeasurementUnitGroupServiceContract $service,
        private readonly MeasurementUnitGroupPolicyContract $policy
    ) {
    }

    /**
     * @response MeasurementUnitGroupIndexCollection<MeasurementUnitGroupIndexResource>
     */
    public function index(MeasurementUnitGroupIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new MeasurementUnitGroupIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(MeasurementUnitGroupStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->authorizeCreate($request, $request->toDTO());

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response MeasurementUnitGroupShowResource
     */
    public function show(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(MeasurementUnitGroupShowResource::make($data));
        });
    }

    public function update(MeasurementUnitGroupUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
