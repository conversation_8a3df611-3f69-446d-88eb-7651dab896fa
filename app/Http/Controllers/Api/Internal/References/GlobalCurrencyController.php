<?php

namespace App\Http\Controllers\Api\Internal\References;

use App\Contracts\Services\Internal\Directories\GlobalCurrenciesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\GlobalCurrencies\GlobalCurrencyIndexRequest;
use App\Http\Resources\References\GlobalCurrencies\GlobalCurrencyIndexCollection;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GlobalCurrencyController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly GlobalCurrenciesServiceContract $service,
    ) {
    }

    /**
     * @response GlobalCurrencyIndexCollection<GlobalCurrencyIndexResource>
     */
    public function index(GlobalCurrencyIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            $collection = new GlobalCurrencyIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    /**
     * @response GlobalCurrencyShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($id) {
            $data = $this->service->show($id);
            return $this->successResponse(GlobalCurrencyShowResource::make($data));
        });
    }
}
