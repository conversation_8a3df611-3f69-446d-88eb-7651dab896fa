<?php

namespace App\Http\Controllers\Api\Internal\WarehouseControllers\Contacts;

use App\Traits\ApiPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseAddressServiceContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseAddressPolicyContract;
use App\Http\Requests\Api\Internal\Warehouses\Contacts\Address\WarehouseAddressStoreRequest;
use App\Http\Requests\Api\Internal\Warehouses\Contacts\Address\WarehouseAddressIndexRequest;
use App\Http\Requests\Api\Internal\Warehouses\Contacts\Address\WarehouseAddressUpdateRequest;
use App\Http\Resources\Warehouses\Contacts\WarehouseAddressIndexCollection;

class WarehouseAddressController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseAddressServiceContract $service,
        private readonly WarehouseAddressPolicyContract $policy
    ) {
    }

    /**
     * @response WarehouseAddressIndexCollection<WarehouseAddressIndexResource>
     */
    public function index(WarehouseAddressIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);
            $data = $this->service->index($data);
            $collection = new WarehouseAddressIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(WarehouseAddressStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response WarehouseAddressShowResource
     */
    public function show(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(WarehouseAddressShowResource::make($data));
        });
    }

    public function update(WarehouseAddressUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
