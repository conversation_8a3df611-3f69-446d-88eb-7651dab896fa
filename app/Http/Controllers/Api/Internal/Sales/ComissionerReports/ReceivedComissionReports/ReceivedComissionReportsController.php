<?php

namespace App\Http\Controllers\Api\Internal\Sales\ComissionerReports\ReceivedComissionReports;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Sales\ReceivedComissionReportsPolicyContract;
use App\Contracts\Services\Internal\Sales\ReceivedComissionReportServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ComissionReports\ReceivedComissionReports\ReceivedComissionReportsIndexRequest;
use App\Http\Requests\Api\Internal\ComissionReports\ReceivedComissionReports\ReceivedComissionReportStoreRequest;
use App\Http\Requests\Api\Internal\ComissionReports\ReceivedComissionReports\ReceivedComissionReportUpdateRequest;
use App\Http\Resources\Sales\ComissionReports\ReceivedComissionReportIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ReceivedComissionReportsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ReceivedComissionReportServiceContract $service,
        private readonly ReceivedComissionReportsPolicyContract $policy
    ) {
    }

    /**
     * @response ReceivedComissionReportIndexCollection<ReceivedComissionReportIndexResource>
     */
    public function index(ReceivedComissionReportsIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $data = $this->service->index($data);
            $collection = new ReceivedComissionReportIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(ReceivedComissionReportStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response ReceivedComissionReportShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(ReceivedComissionReportShowResource::make($data));
        });
    }

    public function update(ReceivedComissionReportUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
