<?php

namespace App\Http\Controllers\Api\Internal\Sales\ComissionerReports\IssuedComissionReports;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Sales\IssuedComissionReportsPolicyContract;
use App\Contracts\Services\Internal\Sales\IssuedComissionReportServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ComissionReports\IssuedComissionReports\IssuedComissionReportsIndexRequest;
use App\Http\Requests\Api\Internal\ComissionReports\IssuedComissionReports\IssuedComissionReportStoreRequest;
use App\Http\Requests\Api\Internal\ComissionReports\IssuedComissionReports\IssuedComissionReportUpdateRequest;
use App\Http\Resources\Sales\ComissionReports\IssuedComissionReportIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class IssuedComissionReportsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly IssuedComissionReportServiceContract $service,
        private readonly IssuedComissionReportsPolicyContract $policy
    ) {
    }

    /**
     * @response IssuedComissionReportIndexCollection<IssuedComissionReportIndexResource>
     */
    public function index(IssuedComissionReportsIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $data = $this->service->index($data);
            $collection = new IssuedComissionReportIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(IssuedComissionReportStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response IssuedComissionReportShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(IssuedComissionReportShowResource::make($data));
        });
    }

    public function update(IssuedComissionReportUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
