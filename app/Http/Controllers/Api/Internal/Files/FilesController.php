<?php

namespace App\Http\Controllers\Api\Internal\Files;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\FilePolicyContract;
use App\Contracts\Services\Internal\FilesServiceContract;
use App\Exceptions\NotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Files\FileIndexRequest;
use App\Http\Requests\Api\Internal\Files\FileStoreRequest;
use App\Http\Resources\Files\FileIndexCollection;
use App\Traits\ApiPolicy;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Symfony\Component\HttpFoundation\StreamedResponse;

class FilesController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly FilesServiceContract $service,
        private readonly FilePolicyContract $policy
    ) {
    }

    /**
     * @response FileIndexCollection<FileIndexResource>
     */
    public function index(FileIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $result = $this->service->index($data);
            $collection = new FileIndexCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    /**
     * @response FileShowResource
     */
    public function show(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(FileShowResource::make($data));
        });
    }

    public function store(FileStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $dto = $request->toDTO();

            $this->authorizeCreate($request, $dto);

            $id = $this->service->create($dto);
            return $this->createdResponse($id);
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function download(Request $request, string $id): StreamedResponse|JsonResponse
    {
        try {
            $this->authorizeView($request, $id);

            return $this->service->download($id);
        } catch (\App\Exceptions\AccessDeniedException $e) {
            return response()->json($e->getMessage(), ResponseAlias::HTTP_FORBIDDEN);
        } catch (NotFoundException $e) {
            return response()->json($e->getMessage(), ResponseAlias::HTTP_NOT_FOUND);
        } catch (Exception $e) {
            return response()->json('An error occurred while processing your request.' . $e->getMessage(), 500);
        }
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
