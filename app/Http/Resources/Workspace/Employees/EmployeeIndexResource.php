<?php

namespace App\Http\Resources\Workspace\Employees;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор сотрудника */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var int $user_id Идентификатор пользователя */
            'user_id' => (int) $this->user_id,
            /** @var string|null $lastname Фамилия */
            'lastname' => $this->lastname,
            /** @var string $firstname Имя */
            'firstname' => (string) $this->firstname,
            /** @var string|null $patronymic Отчество */
            'patronymic' => $this->patronymic,
            /** @var string|null $telephone Телефон */
            'telephone' => $this->telephone,
            /** @var string $email Email */
            'email' => (string) $this->email,
            /** @var string $status Статус */
            'status' => (string) $this->status,
            /** @var string|null $job_number Табельный номер */
            'job_number' => $this->job_number,
            /** @var string|null $citizenship Гражданство */
            'citizenship' => $this->citizenship,
            /** @var string|null $gender Пол */
            'gender' => $this->gender,
            /** @var string|null $inn ИНН */
            'inn' => $this->inn,
            /** @var string|null $id_card Удостоверение личности */
            'id_card' => $this->id_card,
            /** @var string|null $passport_series Серия паспорта */
            'passport_series' => $this->passport_series,
            /** @var string|null $passport_number Номер паспорта */
            'passport_number' => $this->passport_number,
            /** @var string|null $passport_issue_date Дата выдачи паспорта */
            'passport_issue_date' => $this->passport_issue_date,
            /** @var string|null $who_issued_passport Кем выдан паспорт */
            'who_issued_passport' => $this->who_issued_passport,
            /** @var string|null $division_code Код подразделения */
            'division_code' => $this->division_code,
            /** @var string|null $registration_address Адрес регистрации */
            'registration_address' => $this->registration_address,
            /** @var string|null $temporary_registration_address Адрес временной регистрации */
            'temporary_registration_address' => $this->temporary_registration_address,
            /** @var string|null $driver_license_series Серия водительского удостоверения */
            'driver_license_series' => $this->driver_license_series,
            /** @var string|null $driver_license_number Номер водительского удостоверения */
            'driver_license_number' => $this->driver_license_number,
            /** @var string|null $driver_license_issue_date Дата выдачи водительского удостоверения */
            'driver_license_issue_date' => $this->driver_license_issue_date,
            /** @var string|null $driver_license_expiration_date Дата окончания водительского удостоверения */
            'driver_license_expiration_date' => $this->driver_license_expiration_date,
            /** @var string|null $driver_license_category Категория водительского удостоверения */
            'driver_license_category' => $this->driver_license_category,
            /** @var string|null $military_card Военный билет */
            'military_card' => $this->military_card,
            /** @var string|null $hire_date Дата приема на работу */
            'hire_date' => $this->hire_date,
            /** @var string|null $dismissal_date Дата увольнения */
            'dismissal_date' => $this->dismissal_date,
            /** @var string|null $position Должность */
            'position' => $this->position,
            /** @var string|null $salary Зарплата */
            'salary' => $this->salary ? (string) $this->salary : null,
            /** @var string|null $labor_fund Фонд оплаты труда */
            'labor_fund' => $this->labor_fund,
            /** @var string|null $planned_advance Планируемый аванс */
            'planned_advance' => $this->planned_advance ? (string) $this->planned_advance : null,
            /** @var array $work_schedule График работы */
            'work_schedule' => $this->work_schedule ? (array) $this->work_schedule : [],
            /** @var string|null $role_id Идентификатор роли */
            'role_id' => $this->role_id,
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
        ];
    }
}
