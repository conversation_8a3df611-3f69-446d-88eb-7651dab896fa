<?php

namespace App\Http\Resources\Workspace\Cabinet;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CabinetInviteIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор приглашения */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $email Email приглашенного */
            'email' => (string) $this->email,
            /** @var string $status Статус приглашения */
            'status' => (string) $this->status,
            /** @var string $token Токен приглашения */
            'token' => (string) $this->token,
            /** @var string|null $role_id Идентификатор роли */
            'role_id' => $this->role_id,
            /** @var array{id: string, firstname: string, lastname: string|null, email: string}|null $employee Сотрудник */
            'employee' => $this->when(!empty($this->employee), [
                'id' => $this->employee['id'],
                'firstname' => $this->employee['firstname'],
                'lastname' => $this->employee['lastname'] ?? null,
                'email' => $this->employee['email'],
            ]),
            /** @var array{id: string, name: string}|null $department Отдел */
            'department' => $this->when(!empty($this->department), [
                'id' => $this->department['id'],
                'name' => $this->department['name'],
            ]),
            /** @var array{id: string, name: string}|null $role Роль */
            'role' => $this->when(!empty($this->role), [
                'id' => $this->role['id'],
                'name' => $this->role['name'],
            ]),
        ];
    }
}
