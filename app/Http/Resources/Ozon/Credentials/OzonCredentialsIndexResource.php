<?php

namespace App\Http\Resources\Ozon\Credentials;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonCredentialsIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор учетных данных Ozon */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $name Название учетных данных */
            'name' => (string) $this->name,
            /** @var string $client_id Client ID для Ozon API */
            'client_id' => (string) $this->client_id,
            /** @var string $api_key API ключ для Ozon API */
            'api_key' => (string) $this->api_key,
        ];
    }
}
