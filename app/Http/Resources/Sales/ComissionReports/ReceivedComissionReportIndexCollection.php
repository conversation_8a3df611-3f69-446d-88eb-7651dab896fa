<?php

namespace App\Http\Resources\Sales\ComissionReports;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ReceivedComissionReportIndexCollection extends ResourceCollection
{
    public $resource = ReceivedComissionReportIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
