<?php

namespace App\Http\Resources\Sales\ComissionReports;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IssuedComissionReportShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор отчета */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var string $number Номер отчета */
            'number' => (string) $this->number,
            /** @var string $date_from Дата отчета */
            'date_from' => $this->date_from,
            /** @var bool $is_held Проведен */
            'is_held' => (bool) $this->is_held,
            /** @var bool $is_printed Напечатан */
            'is_printed' => (bool) $this->is_printed,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->sales_channel_id,
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->currency_id,
            /** @var string $sum Сумма */
            'sum' => (string) $this->sum,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $comission_type Тип комиссии */
            'comission_type' => (string) $this->comission_type,
            /** @var string $comission_value Значение комиссии */
            'comission_value' => (string) $this->comission_value,
            /** @var bool $is_common Общий отчет */
            'is_common' => (bool) $this->is_common,
            /** @var string $period_from Период с */
            'period_from' => $this->period_from,
            /** @var string $period_to Период по */
            'period_to' => $this->period_to,
            /** @var string $contract_id Идентификатор контракта */
            'contract_id' => $this->contract_id,
        ];
    }
}
