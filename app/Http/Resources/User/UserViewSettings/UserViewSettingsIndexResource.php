<?php

namespace App\Http\Resources\User\UserViewSettings;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserViewSettingsIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор настройки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $name Название настройки */
            'name' => (string) $this->name,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var array $settings Настройки */
            'settings' => !empty($this->settings) ? json_decode($this->settings, true) : [],
        ];
    }
}
