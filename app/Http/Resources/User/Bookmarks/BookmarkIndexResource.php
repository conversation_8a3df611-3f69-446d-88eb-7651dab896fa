<?php

namespace App\Http\Resources\User\Bookmarks;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookmarkIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор закладки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $name Название закладки */
            'name' => (string) $this->name,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $entity Сущность */
            'entity' => (string) $this->entity,
            /** @var array $filters Фильтры */
            'filters' => !empty($this->filters) ? json_decode($this->filters, true) : [],
        ];
    }
}
