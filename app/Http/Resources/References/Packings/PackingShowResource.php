<?php

namespace App\Http\Resources\References\Packings;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PackingShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор упаковки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $name Название упаковки */
            'name' => (string) $this->name,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string|null $length Длина */
            'length' => $this->length ? (string) $this->length : null,
            /** @var string|null $width Ширина */
            'width' => $this->width ? (string) $this->width : null,
            /** @var string|null $height Высота */
            'height' => $this->height ? (string) $this->height : null,
            /** @var array{id: string, name: string|null, short_name: string|null, code: string|null, conversion_factor: string, cabinet_id: string|null, group_id: string, is_default: bool} $measurement_unit_size Единица измерения размера */
            'measurement_unit_size' => !empty($this->measurement_unit_size) ? [
                'id' => $this->measurement_unit_size->id,
                'name' => $this->measurement_unit_size->name,
                'short_name' => $this->measurement_unit_size->short_name,
                'code' => $this->measurement_unit_size->code,
                'conversion_factor' => (string) $this->measurement_unit_size->conversion_factor,
                'cabinet_id' => $this->measurement_unit_size->cabinet_id,
                'group_id' => $this->measurement_unit_size->group_id,
                'is_default' => (bool) $this->measurement_unit_size->is_default,
            ] : (object) [],
            /** @var string|null $weight Вес */
            'weight' => $this->weight ? (string) $this->weight : null,
            /** @var array{id: string, name: string|null, short_name: string|null, code: string|null, conversion_factor: string, cabinet_id: string|null, group_id: string, is_default: bool} $measurement_unit_weight Единица измерения веса */
            'measurement_unit_weight' => !empty($this->measurement_unit_weight) ? [
                'id' => $this->measurement_unit_weight->id,
                'name' => $this->measurement_unit_weight->name,
                'short_name' => $this->measurement_unit_weight->short_name,
                'code' => $this->measurement_unit_weight->code,
                'conversion_factor' => (string) $this->measurement_unit_weight->conversion_factor,
                'cabinet_id' => $this->measurement_unit_weight->cabinet_id,
                'group_id' => $this->measurement_unit_weight->group_id,
                'is_default' => (bool) $this->measurement_unit_weight->is_default,
            ] : (object) [],
            /** @var string|null $volume Объем */
            'volume' => $this->volume ? (string) $this->volume : null,
            /** @var array{id: string, name: string|null, short_name: string|null, code: string|null, conversion_factor: string, cabinet_id: string|null, group_id: string, is_default: bool} $measurement_unit_volume Единица измерения объема */
            'measurement_unit_volume' => !empty($this->measurement_unit_volume) ? [
                'id' => $this->measurement_unit_volume->id,
                'name' => $this->measurement_unit_volume->name,
                'short_name' => $this->measurement_unit_volume->short_name,
                'code' => $this->measurement_unit_volume->code,
                'conversion_factor' => (string) $this->measurement_unit_volume->conversion_factor,
                'cabinet_id' => $this->measurement_unit_volume->cabinet_id,
                'group_id' => $this->measurement_unit_volume->group_id,
                'is_default' => (bool) $this->measurement_unit_volume->is_default,
            ] : (object) [],
        ];
    }
}
