<?php

namespace App\Http\Resources\References\Packings;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PackingIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор упаковки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $name Название упаковки */
            'name' => (string) $this->name,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string|null $length Длина */
            'length' => $this->length ? (string) $this->length : null,
            /** @var string|null $width Ширина */
            'width' => $this->width ? (string) $this->width : null,
            /** @var string|null $height Высота */
            'height' => $this->height ? (string) $this->height : null,
            /** @var string $measurement_unit_size_id Идентификатор единицы измерения размера */
            'measurement_unit_size_id' => $this->measurement_unit_size_id,
            /** @var string|null $weight Вес */
            'weight' => $this->weight ? (string) $this->weight : null,
            /** @var string $measurement_unit_weight_id Идентификатор единицы измерения веса */
            'measurement_unit_weight_id' => $this->measurement_unit_weight_id,
            /** @var string|null $volume Объем */
            'volume' => $this->volume ? (string) $this->volume : null,
            /** @var string $measurement_unit_volume_id Идентификатор единицы измерения объема */
            'measurement_unit_volume_id' => $this->measurement_unit_volume_id,
        ];
    }
}
