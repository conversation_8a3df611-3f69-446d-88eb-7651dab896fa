<?php

namespace App\Http\Resources\References\MeasurementUnits;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class MeasurementUnitIndexCollection extends ResourceCollection
{
    public $resource = MeasurementUnitIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
