<?php

namespace App\Http\Resources\References\MeasurementUnits;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MeasurementUnitGroupIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор группы единиц измерения */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $name Название группы единиц измерения */
            'name' => (string) $this->name,
            /** @var string $tech_type Технический тип */
            'tech_type' => (string) $this->tech_type,
            /** @var bool $is_system Системная группа */
            'is_system' => (bool) $this->is_system,
            /** @var string|null $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
        ];
    }
}
