<?php

namespace App\Http\Resources\References\Countries;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CountryIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор страны */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $name Название страны */
            'name' => (string) $this->name,
            /** @var string|null $full_name Полное название страны */
            'full_name' => $this->full_name,
            /** @var string|null $code Код страны */
            'code' => $this->code,
            /** @var string|null $iso2 ISO2 код */
            'iso2' => $this->iso2,
            /** @var string|null $iso3 ISO3 код */
            'iso3' => $this->iso3,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_default По умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var bool $is_common Общая страна */
            'is_common' => (bool) $this->is_common,
        ];
    }
}
