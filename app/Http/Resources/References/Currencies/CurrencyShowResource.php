<?php

namespace App\Http\Resources\References\Currencies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CurrencyShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор валюты */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string|null $currency_id Идентификатор глобальной валюты */
            'currency_id' => $this->currency_id,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var bool $is_accouting Учетная валюта */
            'is_accouting' => (bool) $this->is_accouting,
            /** @var string|null $external_id Внешний идентификатор */
            'external_id' => $this->external_id,
            /** @var string|null $num_code Числовой код */
            'num_code' => $this->num_code,
            /** @var string|null $char_code Символьный код */
            'char_code' => $this->char_code,
            /** @var string|null $short_name Краткое название */
            'short_name' => $this->short_name,
            /** @var string|null $name Название */
            'name' => $this->name,
            /** @var int $type Тип валюты */
            'type' => (int) $this->type,
            /** @var string $markup Наценка */
            'markup' => (string) $this->markup,
            /** @var string $nominal Номинал */
            'nominal' => (string) $this->nominal,
            /** @var string $value Курс */
            'value' => (string) $this->value,
            /** @var bool $is_reverse Обратный курс */
            'is_reverse' => (bool) $this->is_reverse,
            /** @var array|null $pluralization Склонения */
            'pluralization' => $this->pluralization ? json_decode($this->pluralization, true) : null,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_common Общая валюта */
            'is_common' => (bool) $this->is_common,
            /** @var bool $is_other Другая валюта */
            'is_other' => (bool) $this->is_other,
        ];
    }
}
