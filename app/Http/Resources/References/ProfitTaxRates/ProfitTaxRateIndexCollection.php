<?php

namespace App\Http\Resources\References\ProfitTaxRates;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ProfitTaxRateIndexCollection extends ResourceCollection
{
    public $resource = ProfitTaxRateIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
