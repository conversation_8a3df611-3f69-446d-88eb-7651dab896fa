<?php

namespace App\Http\Resources\References\LegalEntities;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LegalEntityIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор юридического лица */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $short_name Краткое название */
            'short_name' => (string) $this->short_name,
            /** @var string|null $code Код */
            'code' => $this->code,
            /** @var string|null $phone Телефон */
            'phone' => $this->phone,
            /** @var string|null $fax Факс */
            'fax' => $this->fax,
            /** @var string|null $email Email */
            'email' => $this->email,
            /** @var string|null $discount_card Дисконтная карта */
            'discount_card' => $this->discount_card,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_default По умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var bool $shared_access Общий доступ */
            'shared_access' => (bool) $this->shared_access,
            /** @var array{id: string, name: string, path: string, size: int, mime_type: string}|null $logo Логотип */
            'logo' => $this->when(!empty($this->logo), [
                'id' => $this->logo['id'],
                'name' => $this->logo['name'],
                'path' => $this->logo['path'],
                'size' => (int) $this->logo['size'],
                'mime_type' => $this->logo['mime_type'],
            ]),
        ];
    }
}
