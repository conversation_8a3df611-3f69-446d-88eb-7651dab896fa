<?php

namespace App\Http\Resources\Documents;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RelatedDocumentShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $documentable_id Идентификатор документа */
            'documentable_id' => $this->documentable_id,
            /** @var string $documentable_type Тип документа */
            'documentable_type' => (string) $this->documentable_type,
            /** @var string $tree_id Идентификатор дерева */
            'tree_id' => $this->tree_id,
            /** @var int $lft Левая граница узла */
            'lft' => (int) $this->lft,
            /** @var int $rgt Правая граница узла */
            'rgt' => (int) $this->rgt,
            /** @var string|null $parent_id Идентификатор родительского документа */
            'parent_id' => $this->parent_id,
            /** @var array $children Дочерние документы */
            'children' => $this->children ? array_map(function ($child) {
                return RelatedDocumentShowResource::make($child)->toArray($request);
            }, $this->children) : [],
        ];
    }
}
