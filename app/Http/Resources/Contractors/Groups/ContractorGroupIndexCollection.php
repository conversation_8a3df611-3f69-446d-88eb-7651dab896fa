<?php

namespace App\Http\Resources\Contractors\Groups;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ContractorGroupIndexCollection extends ResourceCollection
{
    public $resource = ContractorGroupIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
