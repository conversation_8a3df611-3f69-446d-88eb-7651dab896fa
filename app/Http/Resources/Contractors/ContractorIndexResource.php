<?php

namespace App\Http\Resources\Contractors;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор контрагента */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var bool $shared_access Общий доступ */
            'shared_access' => (bool) $this->shared_access,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $title Наименование */
            'title' => (string) $this->title,
            /** @var string $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var bool $is_buyer Покупатель */
            'is_buyer' => (bool) $this->is_buyer,
            /** @var bool $is_supplier Поставщик */
            'is_supplier' => (bool) $this->is_supplier,
            /** @var string|null $phone Телефон */
            'phone' => $this->phone,
            /** @var string|null $fax Факс */
            'fax' => $this->fax,
            /** @var string|null $email Электронный адрес */
            'email' => $this->email,
            /** @var string|null $description Комментарий */
            'description' => $this->description,
            /** @var string|null $code Код */
            'code' => $this->code,
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->external_code,
            /** @var string|null $discounts_and_prices Скидки и цены */
            'discounts_and_prices' => $this->discounts_and_prices,
            /** @var string|null $discount_card_number Номер дисконтной карты */
            'discount_card_number' => $this->discount_card_number,
            /** @var bool $is_default Контрагент по умолчанию */
            'is_default' => (bool) $this->is_default,
        ];
    }
}
