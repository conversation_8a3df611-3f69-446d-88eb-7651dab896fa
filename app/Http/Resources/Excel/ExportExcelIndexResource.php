<?php

namespace App\Http\Resources\Excel;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExportExcelIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор экспорта */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $title Заголовок экспорта */
            'title' => (string) $this->title,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string $file Имя файла */
            'file' => (string) $this->file,
            /** @var string $path Путь к файлу */
            'path' => (string) $this->path,
            /** @var string $mime_type MIME тип файла */
            'mime_type' => (string) $this->mime_type,
            /** @var string|null $log Лог операции */
            'log' => $this->log,
            /** @var int $status Статус экспорта */
            'status' => (int) $this->status,
        ];
    }
}
