<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="AcceptanceResource",
 *     type="object",
 *     @OA\Property(property="id", type="string", format="uuid", description="ID приемки"),
 *     @OA\Property(property="number", type="string", description="Номер приемки"),
 *     @OA\Property(property="date_from", type="string", format="date-time", description="Дата приемки"),
 *     @OA\Property(property="incoming_number", type="string", description="Входящий номер"),
 *     @OA\Property(property="incoming_date", type="string", format="date", description="Входящая дата"),
 *     @OA\Property(property="held", type="boolean", description="Проведено"),
 *     @OA\Property(property="is_common", type="boolean", description="Общий документ"),
 *     @OA\Property(property="comment", type="string", description="Комментарий"),
 *     @OA\Property(property="price_includes_vat", type="boolean", description="Цена включает НДС"),
 *     @OA\Property(property="has_vat", type="boolean", description="Есть НДС"),
 *     @OA\Property(property="overhead_cost", type="number", description="Накладные расходы"),
 *     @OA\Property(property="total_price", type="number", description="Общая сумма"),
 *     @OA\Property(property="currency_value", type="number", description="Курс валюты"),
 *     @OA\Property(property="created_at", type="string", format="date-time", description="Дата создания"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", description="Дата обновления"),
 *     @OA\Property(
 *         property="warehouse",
 *         type="object",
 *         @OA\Property(property="id", type="string", format="uuid"),
 *         @OA\Property(property="name", type="string"),
 *         @OA\Property(property="address", type="string")
 *     ),
 *     @OA\Property(
 *         property="contractor",
 *         type="object",
 *         @OA\Property(property="id", type="string", format="uuid"),
 *         @OA\Property(property="name", type="string"),
 *         @OA\Property(property="inn", type="string")
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="object",
 *         @OA\Property(property="id", type="string", format="uuid"),
 *         @OA\Property(property="name", type="string"),
 *         @OA\Property(property="color", type="string")
 *     ),
 *     @OA\Property(
 *         property="files",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="id", type="string", format="uuid"),
 *             @OA\Property(property="name", type="string"),
 *             @OA\Property(property="url", type="string")
 *         )
 *     )
 * )
 */
class AcceptanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'number' => $this->number,
            'date_from' => $this->date_from,
            'incoming_number' => $this->incoming_number,
            'incoming_date' => $this->incoming_date,
            'held' => $this->held,
            'is_common' => $this->is_common,
            'comment' => $this->comment,
            'price_includes_vat' => $this->price_includes_vat,
            'has_vat' => $this->has_vat,
            'overhead_cost' => $this->overhead_cost,
            'total_price' => $this->total_price,
            'currency_value' => $this->currency_value,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Связанные данные
            'warehouse' => $this->whenLoaded('warehouse', function () {
                return is_string($this->warehouse) ? json_decode($this->warehouse, true) : ($this->warehouse ?? []);
            }),
            'contractor' => $this->whenLoaded('contractor', function () {
                return is_string($this->contractor) ? json_decode($this->contractor, true) : ($this->contractor ?? []);
            }),
            'status' => $this->whenLoaded('status', function () {
                return is_string($this->status) ? json_decode($this->status, true) : ($this->status ?? []);
            }),
            'files' => $this->whenLoaded('files', function () {
                return is_string($this->files) ? json_decode($this->files, true) : ($this->files ?? []);
            }),
        ];
    }
} 