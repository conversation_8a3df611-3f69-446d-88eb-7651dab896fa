<?php

namespace App\Http\Resources\Other\Suggests;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SuggestBankResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $value Значение подсказки */
            'value' => (string) $this['value'],
            /** @var string $unrestricted_value Неограниченное значение */
            'unrestricted_value' => (string) $this['unrestricted_value'],
            /** @var array{opf: array{type: string|null, full: string|null, short: string|null}, name: array{payment: string|null, full: string|null, short: string|null}, bic: string|null, swift: string|null, inn: string|null, kpp: string|null, okpo: string|null, correspondent_account: string|null, treasury_accounts: array|null, registration_number: string|null, rkc: array|null, address: array{value: string|null, unrestricted_value: string|null, data: array}, phone: string|null, state: array{status: string|null, actuality_date: string|null, registration_date: string|null, liquidation_date: string|null}} $data Данные банка */
            'data' => $this['data'] ?? (object) [],
        ];
    }
}
