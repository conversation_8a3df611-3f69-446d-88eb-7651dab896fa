<?php

namespace App\Http\Resources\Money\OutgoingPayments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OutgoingPaymentItemShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор позиции исходящего платежа */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $outgoing_payment_id Идентификатор исходящего платежа */
            'outgoing_payment_id' => $this->outgoing_payment_id,
            /** @var string $document_id Идентификатор документа */
            'document_id' => $this->document_id,
            /** @var string $paid_in Сумма к оплате */
            'paid_in' => (string) $this->paid_in,
        ];
    }
}
