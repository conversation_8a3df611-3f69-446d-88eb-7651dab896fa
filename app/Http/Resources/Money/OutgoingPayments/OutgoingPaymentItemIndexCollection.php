<?php

namespace App\Http\Resources\Money\OutgoingPayments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OutgoingPaymentItemIndexCollection extends ResourceCollection
{
    public $resource = OutgoingPaymentItemIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
