<?php

namespace App\Http\Resources\Goods\Attributes\Groups;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class AttributeGroupIndexCollection extends ResourceCollection
{
    public $resource = AttributeGroupIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
