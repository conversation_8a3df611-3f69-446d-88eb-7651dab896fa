<?php

namespace App\Http\Resources\Goods\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор товара */
            'id' => $this->id,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $title Название товара */
            'title' => (string) $this->title,
            /** @var string $short_title Короткое название */
            'short_title' => (string) $this->short_title,
            /** @var int $type Тип товара */
            'type' => (int) $this->type,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string|null $short_description Короткое описание */
            'short_description' => $this->short_description,
            /** @var bool $discounts_retail_sales Запретить скидки при продаже в розницу */
            'discounts_retail_sales' => (bool) $this->discounts_retail_sales,
            /** @var string|null $product_group_id Идентификатор группы товаров */
            'product_group_id' => $this->product_group_id,
            /** @var string|null $article Артикул */
            'article' => $this->article,
            /** @var string|null $code Код товара */
            'code' => $this->code,
            /** @var int|null $inner_code Внутренний код */
            'inner_code' => $this->inner_code ? (int) $this->inner_code : null,
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->external_code,
            /** @var string|null $measurement_unit_id Идентификатор единицы измерения */
            'measurement_unit_id' => $this->measurement_unit_id,
            /** @var string|null $brand_id Идентификатор бренда */
            'brand_id' => $this->brand_id,
            /** @var string $min_price Минимальная цена */
            'min_price' => (string) $this->min_price,
            /** @var string|null $min_price_currency_id Валюта минимальной цены */
            'min_price_currency_id' => $this->min_price_currency_id,
            /** @var string $purchase_price Закупочная цена */
            'purchase_price' => (string) $this->purchase_price,
            /** @var string|null $purchase_price_currency_id Валюта закупочной цены */
            'purchase_price_currency_id' => $this->purchase_price_currency_id,
            /** @var int|null $threshold_type Тип порога */
            'threshold_type' => $this->threshold_type ? (int) $this->threshold_type : null,
            /** @var int|null $threshold_count Количество порога */
            'threshold_count' => $this->threshold_count ? (int) $this->threshold_count : null,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string|null $category_id Идентификатор категории */
            'category_id' => $this->category_id,
            /** @var bool $is_default Товар по умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var string $length Длина */
            'length' => (string) $this->length,
            /** @var string $width Ширина */
            'width' => (string) $this->width,
            /** @var string $height Высота */
            'height' => (string) $this->height,
            /** @var string $weight Вес */
            'weight' => (string) $this->weight,
            /** @var string $volume Объем */
            'volume' => (string) $this->volume,
            /** @var string|null $tax_id Идентификатор налога */
            'tax_id' => $this->tax_id,
            /** @var string|null $tax_system Система налогообложения */
            'tax_system' => $this->tax_system,
            /** @var string|null $indication_subject_calculation Признак предмета расчета */
            'indication_subject_calculation' => $this->indication_subject_calculation,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $articles Артикулы (агрегированные) */
            'articles' => $this->articles,
            /** @var string|null $egais_codes Коды ЕГАИС */
            'egais_codes' => $this->egais_codes,
            /** @var string|null $image URL изображения */
            'image' => $this->image,
            /** @var array{id: string, value: string, type: string, sort: int}[] $barcodes Штрихкоды */
            'barcodes' => $this->barcodes ? array_map(function($barcode) {
                return [
                    'id' => $barcode['id'],
                    'value' => $barcode['value'],
                    'type' => $barcode['type'],
                    'sort' => (int) $barcode['sort'],
                ];
            }, $this->barcodes) : [],
            /** @var array{id: string, name: string} $categories Категория */
            'categories' => !empty($this->categories) ? [
                'id' => $this->categories['id'],
                'name' => $this->categories['name'],
            ] : (object) [],
            /** @var array{id: string, name: string} $measurement_units Единица измерения */
            'measurement_units' => !empty($this->measurement_units) ? [
                'id' => $this->measurement_units['id'],
                'name' => $this->measurement_units['name'],
            ] : (object) [],
            /** @var array{id: string, name: string} $brands Бренд */
            'brands' => !empty($this->brands) ? [
                'id' => $this->brands['id'],
                'name' => $this->brands['name'],
            ] : (object) [],
            /** @var array{id: string, title: string} $contractors Контрагент */
            'contractors' => !empty($this->contractors) ? [
                'id' => $this->contractors['id'],
                'title' => $this->contractors['title'],
            ] : (object) [],
            /** @var array{id: string, name: string} $country Страна */
            'country' => !empty($this->country) ? [
                'id' => $this->country['id'],
                'name' => $this->country['name'],
            ] : (object) [],
            /** @var array{id: string, name: string} $product_groups Группа товаров */
            'product_groups' => !empty($this->product_groups) ? [
                'id' => $this->product_groups['id'],
                'name' => $this->product_groups['name'],
            ] : (object) [],
            /** @var array{pack_type: int|null, type_accounting: int|null, accounting_series: bool|null, product_siz_name_id: string|null, product_siz_type_id: string|null, product_type_code: string|null, container_capacity: string|null, strength: string|null, excise: bool|null, tnwed_id: string|null, target_gender: int|null, type_production: int|null, age_category: int|null, set: bool|null, partial_sale: bool|null, model: string|null, traceable: bool|null} $features Особенности учета */
            'features' => !empty($this->features) ? [
                'pack_type' => $this->features['pack_type'] ? (int) $this->features['pack_type'] : null,
                'type_accounting' => $this->features['type_accounting'] ? (int) $this->features['type_accounting'] : null,
                'accounting_series' => $this->features['accounting_series'] ? (bool) $this->features['accounting_series'] : null,
                'product_siz_name_id' => $this->features['product_siz_name_id'] ?? null,
                'product_siz_type_id' => $this->features['product_siz_type_id'] ?? null,
                'product_type_code' => $this->features['product_type_code'] ?? null,
                'container_capacity' => $this->features['container_capacity'] ?? null,
                'strength' => $this->features['strength'] ?? null,
                'excise' => $this->features['excise'] ? (bool) $this->features['excise'] : null,
                'tnwed_id' => $this->features['tnwed_id'] ?? null,
                'target_gender' => $this->features['target_gender'] ? (int) $this->features['target_gender'] : null,
                'type_production' => $this->features['type_production'] ? (int) $this->features['type_production'] : null,
                'age_category' => $this->features['age_category'] ? (int) $this->features['age_category'] : null,
                'set' => $this->features['set'] ? (bool) $this->features['set'] : null,
                'partial_sale' => $this->features['partial_sale'] ? (bool) $this->features['partial_sale'] : null,
                'model' => $this->features['model'] ?? null,
                'traceable' => $this->features['traceable'] ? (bool) $this->features['traceable'] : null,
            ] : (object) [],
            /** @var array{id: string, name: string, description: string|null, length: string, width: string, height: string, weight: string, volume: string, barcodes: array{id: string, value: string, type: string, sort: int}[]}[] $packings Упаковки */
            'packings' => $this->packings ? array_map(function($packing) {
                return [
                    'id' => $packing['id'],
                    'name' => $packing['name'],
                    'description' => $packing['description'] ?? null,
                    'length' => (string) $packing['length'],
                    'width' => (string) $packing['width'],
                    'height' => (string) $packing['height'],
                    'weight' => (string) $packing['weight'],
                    'volume' => (string) $packing['volume'],
                    'barcodes' => $packing['barcodes'] ? array_map(function($barcode) {
                        return [
                            'id' => $barcode['id'],
                            'value' => $barcode['value'],
                            'type' => $barcode['type'],
                            'sort' => (int) $barcode['sort'],
                        ];
                    }, $packing['barcodes']) : [],
                ];
            }, $this->packings) : [],
            /** @var array{atr_id: string, atr_val: string}[] $product_attribute Атрибуты товара */
            'product_attribute' => $this->product_attribute ? array_map(function($attribute) {
                return [
                    'atr_id' => $attribute['atr_id'],
                    'atr_val' => $attribute['atr_val'],
                ];
            }, $this->product_attribute) : [],
        ];
    }
}
