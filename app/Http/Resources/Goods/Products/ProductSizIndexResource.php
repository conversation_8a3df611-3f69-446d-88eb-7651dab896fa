<?php

namespace App\Http\Resources\Goods\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductSizIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var array{id: string, name: string}[] $sizNames Названия размеров */
            'sizNames' => array_map(function($sizName) {
                return [
                    'id' => $sizName->id,
                    'name' => (string) $sizName->name,
                ];
            }, $this['sizNames']->toArray()),
            /** @var array{id: string, name: string}[] $sizTypes Типы размеров */
            'sizTypes' => array_map(function($sizType) {
                return [
                    'id' => $sizType->id,
                    'name' => (string) $sizType->name,
                ];
            }, $this['sizTypes']->toArray()),
        ];
    }
}
