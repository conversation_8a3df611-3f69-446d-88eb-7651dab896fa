<?php

namespace App\Http\Resources\Goods\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор товара */
            'id' => $this->id,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $title Название товара */
            'title' => (string) $this->title,
            /** @var string $short_title Короткое название */
            'short_title' => (string) $this->short_title,
            /** @var int $type Тип товара */
            'type' => (int) $this->type,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string|null $short_description Короткое описание */
            'short_description' => $this->short_description,
            /** @var bool $discounts_retail_sales Запретить скидки при продаже в розницу */
            'discounts_retail_sales' => (bool) $this->discounts_retail_sales,
            /** @var array{id: string, name: string}|null $group Группа товаров */
            'group' => $this->when(!empty($this->group), [
                'id' => $this->group['id'],
                'name' => $this->group['name'],
            ]),
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->country_id,
            /** @var string|null $article Артикул */
            'article' => $this->article,
            /** @var string|null $code Код товара */
            'code' => $this->code,
            /** @var int|null $inner_code Внутренний код */
            'inner_code' => $this->inner_code ? (int) $this->inner_code : null,
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->external_code,
            /** @var string|null $measurement_unit_id Идентификатор единицы измерения */
            'measurement_unit_id' => $this->measurement_unit_id,
            /** @var string|null $brand_id Идентификатор бренда */
            'brand_id' => $this->brand_id,
            /** @var string $min_price Минимальная цена */
            'min_price' => (string) $this->min_price,
            /** @var string|null $min_price_currency_id Валюта минимальной цены */
            'min_price_currency_id' => $this->min_price_currency_id,
            /** @var string $purchase_price Закупочная цена */
            'purchase_price' => (string) $this->purchase_price,
            /** @var string|null $purchase_price_currency_id Валюта закупочной цены */
            'purchase_price_currency_id' => $this->purchase_price_currency_id,
            /** @var string $length Длина */
            'length' => (string) $this->length,
            /** @var string $width Ширина */
            'width' => (string) $this->width,
            /** @var string $weight Вес */
            'weight' => (string) $this->weight,
            /** @var string $volume Объем */
            'volume' => (string) $this->volume,
            /** @var string|null $tax_id Идентификатор налога */
            'tax_id' => $this->tax_id,
            /** @var string|null $tax_system Система налогообложения */
            'tax_system' => $this->tax_system,
            /** @var string|null $indication_subject_calculation Признак предмета расчета */
            'indication_subject_calculation' => $this->indication_subject_calculation,
            /** @var int|null $threshold_type Тип порога */
            'threshold_type' => $this->threshold_type ? (int) $this->threshold_type : null,
            /** @var int|null $threshold_count Количество порога */
            'threshold_count' => $this->threshold_count ? (int) $this->threshold_count : null,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string|null $category_id Идентификатор категории */
            'category_id' => $this->category_id,
            /** @var bool $is_default Товар по умолчанию */
            'is_default' => (bool) $this->is_default,
        ];
    }
}
