<?php

namespace App\Http\Resources\Goods\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductAttributeShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор атрибута товара */
            'id' => $this->id,
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->product_id,
            /** @var string $attribute_id Идентификатор атрибута */
            'attribute_id' => $this->attribute_id,
            /** @var string $attribute_values_id Идентификатор значения атрибута */
            'attribute_values_id' => $this->attribute_values_id,
            /** @var int|null $sort_order Порядок сортировки */
            'sort_order' => $this->sort_order ? (int) $this->sort_order : null,
        ];
    }
}
