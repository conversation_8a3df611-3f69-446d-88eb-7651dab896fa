<?php

namespace App\Http\Resources\Goods\Transfers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoodsTransferIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор перемещения товаров */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_common Общее перемещение */
            'is_common' => (bool) $this->is_common,
            /** @var string|null $number Номер перемещения */
            'number' => $this->number,
            /** @var string $date_from Дата перемещения */
            'date_from' => $this->date_from,
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var bool $held Проведено */
            'held' => (bool) $this->held,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $to_warehouse_id Идентификатор склада назначения */
            'to_warehouse_id' => $this->to_warehouse_id,
            /** @var string $from_warehouse_id Идентификатор склада отправления */
            'from_warehouse_id' => $this->from_warehouse_id,
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->currency_id,
            /** @var string $currency_value Курс валюты */
            'currency_value' => (string) $this->currency_value,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
            /** @var string $overhead_cost Накладные расходы */
            'overhead_cost' => (string) $this->overhead_cost,
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,
        ];
    }
}
