<?php

namespace App\Http\Resources\Goods\Transfers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoodsTransferItemIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор позиции перемещения товаров */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $goods_transfer_id Идентификатор перемещения товаров */
            'goods_transfer_id' => $this->goods_transfer_id,
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->product_id,
            /** @var int $quantity Количество */
            'quantity' => (int) $this->quantity,
            /** @var string $price Цена */
            'price' => (string) $this->price,
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,
            /** @var string $recidual_from Остаток с */
            'recidual_from' => (string) $this->recidual_from,
            /** @var string $recidual_to Остаток по */
            'recidual_to' => (string) $this->recidual_to,
        ];
    }
}
