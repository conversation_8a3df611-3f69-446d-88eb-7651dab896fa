<?php

namespace App\Http\Resources\Goods\Transfers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class GoodsTransferItemIndexCollection extends ResourceCollection
{
    public $resource = GoodsTransferItemIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
