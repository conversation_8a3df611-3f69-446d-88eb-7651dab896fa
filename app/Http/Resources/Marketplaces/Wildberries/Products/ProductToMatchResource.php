<?php

namespace App\Http\Resources\Marketplaces\Wildberries\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductToMatchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор товара к сопоставлению */
            'id' => $this->id,
            /** @var string $created_at Дата создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $wildberries_integration_id Идентификатор интеграции Wildberries */
            'wildberries_integration_id' => $this->wildberries_integration_id,
            /** @var int $wb_id Идентификатор товара в Wildberries */
            'wb_id' => (int) $this->wb_id,
            /** @var int|null $size_id Идентификатор размера */
            'size_id' => $this->size_id ? (int) $this->size_id : null,
            /** @var string|null $size_name Название размера */
            'size_name' => $this->size_name,
            /** @var string|null $tech_size Технический размер */
            'tech_size' => $this->tech_size,
            /** @var string $title Название товара */
            'title' => $this->title,
            /** @var string|null $description Описание товара */
            'description' => $this->description,
            /** @var string $vendor_code Артикул поставщика */
            'vendor_code' => $this->vendor_code,
            /** @var string $brand Бренд товара */
            'brand' => $this->brand,
            /** @var array $skus Массив SKU товара */
            'skus' => $this->skus ?? [],
            /** @var string|null $price Цена товара */
            'price' => $this->price ? (string) $this->price : null,
            /** @var string|null $discount_price Цена со скидкой */
            'discount_price' => $this->discount_price ? (string) $this->discount_price : null,
            /** @var bool $is_matched Признак сопоставления */
            'is_matched' => (bool) $this->is_matched,
            /** @var string|null $matched_product_id Идентификатор сопоставленного товара */
            'matched_product_id' => $this->matched_product_id,
            /** @var array $suggested_matches Предлагаемые варианты сопоставления */
            'suggested_matches' => $this->suggested_matches ?? [],
        ];
    }
}
