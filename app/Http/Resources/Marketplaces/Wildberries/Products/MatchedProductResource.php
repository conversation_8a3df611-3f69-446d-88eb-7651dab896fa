<?php

namespace App\Http\Resources\Marketplaces\Wildberries\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MatchedProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор сопоставленного товара */
            'id' => $this->id,
            /** @var string $created_at Дата создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $wildberries_integration_id Идентификатор интеграции Wildberries */
            'wildberries_integration_id' => $this->wildberries_integration_id,
            /** @var string|null $product_id Идентификатор товара в системе */
            'product_id' => $this->product_id,
            /** @var int $wb_id Идентификатор товара в Wildberries */
            'wb_id' => (int) $this->wb_id,
            /** @var int|null $size_id Идентификатор размера */
            'size_id' => $this->size_id ? (int) $this->size_id : null,
            /** @var string|null $size_name Название размера */
            'size_name' => $this->size_name,
            /** @var string $title Название товара */
            'title' => $this->title,
            /** @var string $vendor_code Артикул поставщика */
            'vendor_code' => $this->vendor_code,
            /** @var array $skus Массив SKU товара */
            'skus' => $this->skus ?? [],
            /** @var string $match_type Тип сопоставления */
            'match_type' => $this->match_type,
            /** @var bool $is_created Признак создания товара */
            'is_created' => (bool) $this->is_created,

            // Поля из JOIN с таблицей products
            /** @var string|null $product_title Название товара в системе */
            'product_title' => $this->product_title,
            /** @var string|null $product_article Артикул товара в системе */
            'product_article' => $this->product_article,
            /** @var string|null $product_code Код товара в системе */
            'product_code' => $this->product_code,
        ];
    }
}
