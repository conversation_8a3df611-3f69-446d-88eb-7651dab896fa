<?php

namespace App\Http\Resources\Marketplaces\Ozon\FBO\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClusterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var int $cluster_id Идентификатор кластера */
            'cluster_id' => (int) $this->cluster_id,
            /** @var string $cluster_name Название кластера */
            'cluster_name' => $this->cluster_name,
            /** @var string $cluster_type Тип кластера */
            'cluster_type' => $this->cluster_type,
            /** @var array<int, array{warehouse_id: int, warehouse_name: string}> $warehouses Склады в кластере */
            'warehouses' => collect($this->warehouses ?? [])->map(function ($warehouse) {
                return [
                    /** @var int $warehouse_id Идентификатор склада */
                    'warehouse_id' => (int) $warehouse->warehouse_id,
                    /** @var string $warehouse_name Название склада */
                    'warehouse_name' => $warehouse->warehouse_name,
                ];
            })->toArray(),
        ];
    }
}
