<?php

namespace App\Http\Resources\Marketplaces\Ozon\FBO\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ClusterCollection extends ResourceCollection
{
    public $collects = ClusterResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'clusters' => $this->collection,
        ];
    }
}
