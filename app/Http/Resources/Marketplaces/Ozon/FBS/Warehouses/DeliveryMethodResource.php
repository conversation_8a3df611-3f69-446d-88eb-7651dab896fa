<?php

namespace App\Http\Resources\Marketplaces\Ozon\FBS\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryMethodResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор метода доставки */
            'id' => $this->id,
            /** @var int $ozon_delivery_method_id Идентификатор метода доставки в Ozon */
            'ozon_delivery_method_id' => (int) $this->ozon_delivery_method_id,
            /** @var string $name Название метода доставки */
            'name' => $this->name,
            /** @var string $status Статус метода доставки */
            'status' => $this->status,
            /** @var string|null $cutoff Время отсечки */
            'cutoff' => $this->cutoff,
            /** @var string|null $sla_cut_in SLA время */
            'sla_cut_in' => $this->sla_cut_in,
            /** @var int|null $provider_id Идентификатор провайдера */
            'provider_id' => $this->provider_id ? (int) $this->provider_id : null,
            /** @var int|null $template_id Идентификатор шаблона */
            'template_id' => $this->template_id ? (int) $this->template_id : null,
            /** @var int|null $company_id Идентификатор компании */
            'company_id' => $this->company_id ? (int) $this->company_id : null,
            /** @var string|null $created_at Дата создания в Ozon */
            'created_at' => $this->created_at,
            /** @var string|null $updated_at Дата обновления в Ozon */
            'updated_at' => $this->updated_at,
        ];
    }
}
