<?php

namespace App\Http\Resources\Marketplaces\Ozon\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductToMatchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор товара к сопоставлению */
            'id' => $this->id,
            /** @var string $created_at Дата создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $integration_id Идентификатор интеграции Ozon */
            'integration_id' => $this->integration_id,
            /** @var int $ozon_product_id Идентификатор товара в Ozon */
            'ozon_product_id' => (int) $this->ozon_product_id,
            /** @var string $title Название товара */
            'title' => $this->title,
            /** @var string|null $description Описание товара */
            'description' => $this->description,
            /** @var string $offer_id Артикул товара (offer_id) */
            'offer_id' => $this->offer_id,
            /** @var string|null $sku SKU товара */
            'sku' => $this->sku,
            /** @var array $barcodes Массив штрихкодов товара */
            'barcodes' => $this->barcodes ?? [],
            /** @var string|null $price Цена товара */
            'price' => $this->price ? (string) $this->price : null,
            /** @var string|null $prediscount_price Цена до скидки */
            'prediscount_price' => $this->prediscount_price ? (string) $this->prediscount_price : null,
            /** @var string|null $min_price Минимальная цена */
            'min_price' => $this->min_price ? (string) $this->min_price : null,
            /** @var bool $is_matched Признак сопоставления */
            'is_matched' => (bool) $this->is_matched,
            /** @var string|null $matched_product_id Идентификатор сопоставленного товара */
            'matched_product_id' => $this->matched_product_id,
            /** @var array $suggested_matches Предлагаемые варианты сопоставления */
            'suggested_matches' => $this->suggested_matches ?? [],
        ];
    }
}
