<?php

namespace App\Http\Resources\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $name Название склада */
            'name' => (string) $this->name,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string|null $work_schedule_id Идентификатор рабочего расписания */
            'work_schedule_id' => $this->work_schedule_id,
            /** @var bool $control_free_residuals Контроль свободных остатков */
            'control_free_residuals' => (bool) $this->control_free_residuals,
            /** @var string|null $address_id Идентификатор адреса */
            'address_id' => $this->address_id,
            /** @var string|null $phone_id Идентификатор телефона */
            'phone_id' => $this->phone_id,
            /** @var string|null $responsible_employee_id Идентификатор ответственного сотрудника */
            'responsible_employee_id' => $this->responsible_employee_id,
            /** @var bool $is_default Склад по умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var bool $is_common Общий склад */
            'is_common' => (bool) $this->is_common,
            /** @var string|null $group_id Идентификатор группы складов */
            'group_id' => $this->group_id,
            /** @var array{id: string, phone: string|null, comment: string|null} $phone Телефон */
            'phone' => !empty($this->phone) ? [
                'id' => $this->phone->id,
                'phone' => $this->phone->phone,
                'comment' => $this->phone->comment,
            ] : (object) [],
            /** @var array{id: string, postcode: string|null, country: string|null, region: string|null, city: string|null, street: string|null, house: string|null, office: string|null, other: string|null, comment: string|null} $address Адрес */
            'address' => !empty($this->address) ? [
                'id' => $this->address->id,
                'postcode' => $this->address->postcode,
                'country' => $this->address->country,
                'region' => $this->address->region,
                'city' => $this->address->city,
                'street' => $this->address->street,
                'house' => $this->address->house,
                'office' => $this->address->office,
                'other' => $this->address->other,
                'comment' => $this->address->comment,
            ] : (object) [],
            /** @var array{on_coming_from: string|null, on_shipment_from: string|null, control_operational_balances: bool|null} $order_scheme Ордерная схема */
            'order_scheme' => !empty($this->order_scheme) ? [
                'on_coming_from' => $this->order_scheme->on_coming_from,
                'on_shipment_from' => $this->order_scheme->on_shipment_from,
                'control_operational_balances' => (bool) $this->order_scheme->control_operational_balances,
            ] : (object) [],
            /** @var array{use_premises_from: string|null, cells: string|null, use_cells_from: string|null} $structure Структура */
            'structure' => !empty($this->structure) ? [
                'use_premises_from' => $this->structure->use_premises_from,
                'cells' => $this->structure->cells,
                'use_cells_from' => $this->structure->use_cells_from,
            ] : (object) [],
            /** @var array{id: string, name: string|null, description: string|null}[] $work_shedule Рабочее расписание */
            'work_shedule' => !empty($this->work_shedule) ? array_map(function($schedule) {
                return [
                    'id' => $schedule['id'],
                    'name' => $schedule['name'] ?? null,
                    'description' => $schedule['description'] ?? null,
                ];
            }, $this->work_shedule) : [],
        ];
    }
}
