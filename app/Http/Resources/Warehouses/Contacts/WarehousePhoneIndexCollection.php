<?php

namespace App\Http\Resources\Warehouses\Contacts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class WarehousePhoneIndexCollection extends ResourceCollection
{
    public $resource = WarehousePhoneIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
